import { createIDGetter, withNamespace } from '@/utils/locale';

const namespace = 'h5_ijoin';

export const getID = createIDGetter(namespace);

const locales = {
  tips_ijoin_accept_recommed: 'Accept changes on or before {stateDate} to avoid cancellation of application.',
  tips_ijoin_promo_code: 'Promo Code',
  btn_ijoin_promo_code_apply: 'Apply',
  tips_ijoin_promo_code_applied: 'Promo Code Applied',
  tips_ijoin_promo_code_invalid: 'Invalid Promo Code',
  tips_ijoin_promo_code_label: 'FREE',
  tips_ijoin_outstanding_balance:
    'You seem to have an outstanding balance amounting to {openAmount} from your existing plan.',
  btn_ijoin_pay_outstanding: 'Pay Outstanding Balance Now',
  btn_ijoin_pay_outstanding_close: 'Close',

  btn_aggrement: 'Agree and Continue',
  tips_pre_to_post_aggrement: 'By clicking the “Agree and Continue” button, you agree on above stipulations.',
  tips_pre_to_post_check_desc: 'We are unable to process your upgrade request at this time.',
  tips_pre_to_post_check_desc_detail:
    '<strong>NOTE:</strong> Upgrade is available to users who do not have any outstanding balance, and no active port request.',
  tips_pre_to_post_check_esim_desc_detail: 'Upgrading to Postapid is currently not available to eSIM users yet.',
  tips_pre_to_post_check_result1_title: 'eSIM Compatible!',
  tips_pre_to_post_check_esim_result1_desc:
    'NOTE: Upgrade is available to users who do not have any outstanding balance, and no active port request.',
  btn_pre_to_post_check_esim_result1_close: 'OK, got it!',
  tips_mobile_term_tc:
    'I have read the <a href="https://dito.ph/terms-and-conditions">Terms and Conditions</a> and <a href="https://dito.ph/PostpaidTandC">Supplemental Terms and Conditions</a> for DITO Mobile Postpaid.',
  tips_fwa_postpaid_term_tc:
    'I have read the <a href="https://dito.ph/terms-and-conditions">Terms and Conditions</a> and <a href="https://auto.dito.ph/img/fwaSupplementalTermsAndConditions.pdf">Supplemental Terms and Conditions</a> for DITO Home Postpaid.',
  lb_ijoin_invite_join_us:
    "We're thrilled to have you join us. Kindly input the referral code below if someone referred you.",
  tips_ijoin_invite_code_times_limited:
    'Oops! We noticed that this referral code has been used beyond limit. <a>Learn More</a>',
  tips_ijoin_invite_code_invalid: 'Invalid referral code',
  tips_ijoin_fwa_invite_seller_conflict:
    'Referral code cannot be applied if you are purchasing from any other Sales Partner.',
  tips_ijoin_fwa_invite_seller_conflict_detail:
    'You are about to submit your application. Please note that the Referral Code cannot be applied if you are purchasing from any other Sales Partner.',
  lb_ijoin_cash_payment_method_name: 'Pay for DRP <span>via Cash</span>',
  tips_drp_payment_note_desc:
    '<p><strong>NOTE:</strong> Your application has been approved.</p><p>Please be reminded that your Sales Invoice should have the exact details indicated in your application form.</p>',
  tips_drp_payment_order_id: 'Application Reference No.',

  tips_fwa_select_addr_title: 'ALL SET WITH THE REQUIREMENTS?',
  tips_fwa_select_addr_subs_title: 'For a quick and easy application, make sure you have the following:',
  tips_fwa_select_addr_desc_1: 'One (1) valid ID',
  tips_fwa_select_addr_desc_2: 'Any Proof of Billing listed below:',
  tips_fwa_select_addr_desc_2_1: 'Utility bills',
  tips_fwa_select_addr_desc_2_2: 'Bank-issued documents',
  tips_fwa_select_addr_desc_2_3: 'Other documents with your name and address for delivery or installation.',
  btn_fwa_proceed_to_apply: 'Proceed to Application',
  btn_fwa_go_back: 'Go back',
  tips_fwa_upload_id: 'Upload your ID',
  tips_fwa_upload_id_desc:
    'Valid lD with Signature: Such as all Philippine Government-issued IDs bearing Photo and Signature.',
  tips_fwa_upload_id_front: 'ID Front',
  tips_fwa_upload_id_back: 'ID Back',

  tips_ijoin_fwa_delivery_title: 'Delivery Details',
  tips_ijoin_fwa_delivery_desc: 'Please double check your details to ensure fast delivery.',
  tips_ijoin_fwa_delivery_method_A_title: 'Delivery via Courier',
  tips_ijoin_fwa_delivery_method_A_desc: 'Our logistics partner will deliver the plug-and-play unit to your address.',
  tips_ijoin_fwa_delivery_method_A_label: 'FREE OF CHARGE',
  tips_ijoin_fwa_delivery_method_A_invalid:
    "We're not delivering to your area just yet. Stay tuned-we're expanding soon!",
  tips_ijoin_fwa_delivery_method_D_title: 'DITO Partner Installer',
  tips_ijoin_fwa_delivery_method_D_desc: 'Our authorized representative will help you setup the device.',
  tips_ijoin_fwa_delivery_method_D_label: 'FREE OF CHARGE',
  lb_ijoin_fwa_receiver_details: "RECEIVER'S DETAILS",
  tips_ijoin_fwa_recevice_for_me: 'Someone else will receive for me',
  tips_ijoin_fwa_optional: '(Optional)',
  lb_ijoin_fwa_receiver_name: "Receiver's Full Name",
  lb_ijoin_fwa_receiver_number: "Receiver's Mobile Number",

  tips_ijoin_fwa_success:
    "We have received your payment for your DITO Home 5G WiFi Prepaid Starter Kit<br/>Congratulations on your purchase! We'll be processing it immediately.<br/>Kindly expect delivery of your DITO Home WiFi within X Days",
  tips_ijoin_fwa_postpaid_success:
    "We have received your payment for your DITO Home 5G WiFi Prepaid Starter Kit<br/>Congratulations on your purchase! We'll be processing it immediately.<br/>Kindly expect delivery of your DITO Home WiFi within X Days",
  tips_ijoin_fwa_pay_success:
    '<p>We have received your payment for {offerName}.</p><p>We have forwarded your order to our logistics partner for delivery. Please keep your lines open.</p>',
  tips_ijoin_fwa_postpaid_pay_success:
    '<p>We have received your payment for {offerName}.</p><p>We have forwarded your order to our logistics partner for delivery. Please keep your lines open.</p>',

  tips_ijoin_fwa_failded:
    'We regret to inform you that your application for {offerName} could not be approved at this time. Thank you.',
  tips_ijoin_fwa_failded_recommend: 'You may want to purchase our DITO Home WiFi Prepaid offer instead:',
  btn_ijoin_fwa_failed_proceed: 'Buy Now',
  btn_ijoin_fwa_failed_not_now: 'Not Now',
};

export default {
  ...locales,
  ...withNamespace(namespace, {}),
};
