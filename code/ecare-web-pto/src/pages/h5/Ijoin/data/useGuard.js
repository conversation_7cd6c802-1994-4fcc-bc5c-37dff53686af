import React, { useMemo, useState } from 'react';
import { isEmpty } from 'lodash';
import { history, useDispatch, useLocation } from 'umi';
import { useModel, useMount } from '@/hooks';
import { Environment, IjoinPlanMenuType, KEY_OPEN_IJOIN_IN } from '@/constants/constants';
import bridge, { inApp } from '@/utils/bridge';
import { getOSInfo } from '@/utils/utils';
import PlatformModal from '../components/PlatformModal';
import useBusiness from './useBusiness';
import {
  Business,
  PageKeys,
  getHomePage,
  pipelinesForFWA,
  pipelinesForFWAV2,
  pipelinesForPostpaid,
  withBusiness,
} from '.';

const PRE_TO_POST_HOME_PATH = '/h5/ijoin/pre2post';

const { isTablet, isPc } = getOSInfo();

const openIJoinInApp = () => {
  const redirectURL = `${window.location.origin}/?schemaType=3&schemaUrl=${window.location.href}`;
  window.location.href = redirectURL;
};

export default function useGuard(outerBusiness, opts = {}) {
  const dispatch = useDispatch();
  const { pathname, query } = useLocation();
  const [business] = useBusiness(outerBusiness);
  const [accessible, setAccessible] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const {
    ijoinPlan: { sourceCode, planType, selectedPlan = {}, selectedGood = {} },
  } = useModel(['ijoinPlan']);

  const goHomePageWithCleanup = () => {
    if (opts?.skip) return;

    dispatch({ type: 'ijoinPlan/cleanupGlobally' });
    history.replace(
      withBusiness(
        {
          pathname: getHomePage(business),
          query: {
            sourceCode,
          },
        },
        business,
      ),
    );
  };

  const isContinuing = query?.continuing === 'Y' || query?.update === 'Y'; // 是否从订单详情页回来修改订单数据

  // guard for prepaid to postpaid
  useMount(() => {
    const path = PRE_TO_POST_HOME_PATH;
    if (pathname !== path) return;
    if (!inApp && !isPc) {
      const { origin, href } = window.location;
      window.location.href = `${origin}/?schemaType=3&schemaUrl=${href}`;
      return;
    }
    if (inApp && !bridge.getCurrentUser()?.token) {
      bridge.goToLogin();
    }
  });

  // guard for postpaid
  useMount(() => {
    const whitelist = [PRE_TO_POST_HOME_PATH, ...pipelinesForPostpaid.slice(0, 2).map(p => p.path)];
    if (business !== Business.POSTPAID || whitelist.includes(pathname) || isContinuing) {
      return;
    }

    if (isEmpty(selectedPlan) || (planType === IjoinPlanMenuType.WITH_HANDSET && isEmpty(selectedGood))) {
      goHomePageWithCleanup();
    }
  });

  // guard for fwa postpaid
  useMount(() => {
    const utilSafePages = pipelinesForFWA.findIndex(p => p.key === PageKeys.PLAN);
    const utilSafePagesv2 = pipelinesForFWAV2.findIndex(p => p.key === PageKeys.PLAN);
    const safePages = [
      ...pipelinesForFWA.slice(0, utilSafePages + 1),
      ...pipelinesForFWAV2.slice(0, utilSafePagesv2 + 1),
      { path: '/h5/ijoin/fwa/plan/detail' }, // 详情页, for deeplink
    ];
    const isSafePage = safePages.some(p => pathname === p.path);
    if (business !== Business.FWA_POSTPAID || isContinuing || isSafePage) {
      return;
    }

    if (isEmpty(selectedPlan)) {
      goHomePageWithCleanup();
    }
  });

  // guard for dispatching platform
  useMount(() => {
    if (opts?.ignorePlatform === true) {
      sessionStorage.setItem(KEY_OPEN_IJOIN_IN, inApp ? Environment.APP : Environment.BROWSER);
      return;
    }
    if (!inApp && pathname?.includes('/h5/ijoin') && !isTablet) {
      // 从移动浏览器中打开
      const openIJoinIn = sessionStorage.getItem(KEY_OPEN_IJOIN_IN);
      if (openIJoinIn === Environment.APP) {
        setAccessible(false);
        openIJoinInApp();
      } else if (openIJoinIn === Environment.BROWSER) {
        setAccessible(true);
      } else {
        setAccessible(false);
        setModalVisible(true);
      }
    }
  });

  const render = useMemo(
    () => (
      <PlatformModal
        business={business}
        visible={modalVisible}
        onOk={() => openIJoinInApp()}
        onCancel={() => {
          setAccessible(true);
          setModalVisible(false);
        }}
      />
    ),
    [business, modalVisible],
  );

  return {
    accessible,
    render,
  };
}
