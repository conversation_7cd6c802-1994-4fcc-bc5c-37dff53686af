import { useLocation } from 'umi';
import { useModel } from '@/hooks';
import { Business, BusinessCodes } from '.';

/**
 * 获取业务场景类型
 * @typedef {value typeof Business} Business 业务类型
 * @param {Business} fact 初始化场景
 * @returns {[Business, { isFTTH: boolean; isPostpaid: boolean; isFWA: boolean; isFWAPostpaid: boolean; isFWAPrepaid: boolean; switchingPre2Post: boolean }]} 业务场景
 */
export default function useBusiness(fact) {
  const { query = {} } = useLocation();

  const {
    ijoinPlan: { selectedPlan = {} },
    iJoinPre2Post: { switchingPre2Post = false },
  } = useModel(['ijoinPlan', 'iJoinPre2Post']);

  const business = query?.business || (typeof fact === 'function' ? fact() : fact || Business.POSTPAID);
  const isFTTH = business === Business.FTTH;
  const isPostpaid = business === Business.POSTPAID;
  // FWA 预付费和后付费共用 Business.FWA_POSTPAID
  const isFWA = business?.includes('FWA');
  const isFWAPostpaid = business === Business.FWA_POSTPAID;

  const isFWAPrepaid = isFWA && selectedPlan?.tagList?.some(tag => tag.tagCode === 'FWA');
  const isFMC = selectedPlan?.tagList?.some(tag => tag.tagCode === 'FMC');

  return [
    business,
    {
      isFTTH,
      isPostpaid,
      isFWA,
      isFWAPostpaid,
      isFWAPrepaid,
      isFMC,
      switchingPre2Post,
      businessCode: BusinessCodes[business],
      businessScene: BusinessCodes[business],
    },
  ];
}
