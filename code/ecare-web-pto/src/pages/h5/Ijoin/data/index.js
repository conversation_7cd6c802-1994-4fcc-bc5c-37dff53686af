import React from 'react';
import moment from 'moment';
import { history } from 'umi';
import { PARAM_FORMAT, STANDARD_FORMAT, VOUCHER_FORMAT } from '@/constants/constants';
import { setExtraHeaders } from '@/utils/request';
import { safeJSONParse } from '@/utils/utils';
import { FORM_ITEM_TYPE } from '../components/FormGenerator';

/**
 * ijoin 的场景类型
 */
export const Business = {
  PREPAID: 'BUSINESS_PREPAID',
  POSTPAID: 'BUSINESS_POSTPAID',
  FWA_POSTPAID: 'BUSINESS_FWA_POSTPAID', // FWA 后付费和预付费共用 FWA_POSTPAID
  FTTH: 'BUSINESS_FTTH',
  PRE2POST: 'BUSINESS_PRE2POST',
  FMC: 'BUSINESS_FMC',
};

/**
 * 各种场景下调用 saveOrder 接口时传的 businessScene 参数
 */
export const BusinessCodes = {
  [Business.PREPAID]: 1,
  [Business.POSTPAID]: 2,
  [Business.FTTH]: 3,
  [Business.FWA_POSTPAID]: 4, // FWA(预/后付费)
  [Business.PRE2POST]: 5,
  [Business.FMC]: 6,
};

export const ChannelCode = {
  DEFAULT: 'APP',
  DSCM: 'DSCM',
  POS: 'POS',
  ESAF: 'ESAF',
};

export const HeaderTitle = {
  [Business.FWA_POSTPAID]: 'DITO Home WiFi',
};

/**
 * 查询 plan 时传的 catgCode 参数
 */
export const PlanCategoryCodes = {
  [Business.FTTH]: 'FTTH_IJOIN_POSTPAID',
  [Business.FWA_POSTPAID]: 'FWA_IJOIN_POSTPAID',
};

/**
 * 预览界面隐私协议的 code
 */
export const PrivacyCodes = {
  [Business.POSTPAID]: 'IJOIN_PRIVACY_POLICY',
  [Business.FTTH]: 'FTTH_IJOIN_PRIVACY_POLICY',
  [Business.FWA_POSTPAID]: 'FWA_POSTPAID_IJOIN_PRIVACY_POLICY',
};

/**
 * 草稿单类型
 */
export const DraftOrderType = {
  NEW_APPLICATION: 1,
  ADDITIONAL_LINE: 2,
  CONTINUE_APPLICATION: 3,
};

// 为页面路由添加 business 的查询参数
export const withBusiness = (route, business = Business.POSTPAID) => ({
  ...route,
  query: {
    business,
    ...(route.query || {}),
  },
});

export const PageKeys = {
  HOME: 'Home',
  PLAN: 'PlanSelect',
  NUMBER: 'NumberSelect',
  INVITATION: 'Invitation',
  COVERAGE: 'NetworkCheck',
  ATTACHMENT: 'UploadFile',
  PROFILE: 'CustInfo',
  CONTACT: 'ContactInfo',
  EMPLOYMENT: 'CompanyInfo',
  ADDRESS: 'DeliveryInfo',
  PREVIEW: 'OrderSummary',
  PAYMENT: 'Payment',
  INSTALLATION: 'Installation',
  INSTALLATION_DETAIL: 'InstallationDetail',
};

// 旧版后付费开户
export const pipelines = [
  { key: PageKeys.PLAN, path: '/h5/ijoin/offers' },
  { key: PageKeys.NUMBER, path: '/h5/ijoin/number' },
  { key: PageKeys.COVERAGE, path: '/h5/ijoin/odaCheck' },
  { key: PageKeys.ATTACHMENT, path: '/h5/ijoin/attachment' },
  { key: PageKeys.PROFILE, path: '/h5/ijoin/profile' },
  { key: PageKeys.CONTACT, path: '/h5/ijoin/contact' },
  { key: PageKeys.EMPLOYMENT, path: '/h5/ijoin/employment' },
  { key: PageKeys.ADDRESS, path: '/h5/ijoin/custDeliveryAddr' },
  { key: PageKeys.PREVIEW, path: '/h5/ijoin/preview' },
  { key: PageKeys.PAYMENT, path: '/h5/ijoin/payment' },
];
// 预转后
export const pipelinesV2 = [
  { key: PageKeys.PLAN, path: '/h5/ijoin/offers' },
  { key: PageKeys.NUMBER, path: '/h5/ijoin/number' },
  { key: PageKeys.COVERAGE, path: '/h5/ijoin/new/coverage' },
  { key: PageKeys.PROFILE, path: '/h5/ijoin/new/profile' },
  { key: PageKeys.EMPLOYMENT, path: '/h5/ijoin/new/employment' },
  { key: PageKeys.PREVIEW, path: '/h5/ijoin/new/summary' },
  { key: PageKeys.PAYMENT, path: '/h5/ijoin/payment' },
];
// 支持草稿单的后付费开户
export const pipelinesForPostpaid = [
  { key: PageKeys.HOME, path: '/h5/ijoin/plan' },
  { key: PageKeys.PLAN, path: '/h5/ijoin/offers' },
  { key: PageKeys.INVITATION, path: '/h5/ijoin/invitation' },
  { key: PageKeys.NUMBER, path: '/h5/ijoin/number' },
  { key: PageKeys.COVERAGE, path: '/h5/ijoin/new/coverage' },
  { key: PageKeys.CONTACT, path: '/h5/ijoin/mobile/postpaid/contact' },
  { key: PageKeys.PROFILE, path: '/h5/ijoin/mobile/postpaid/application' },
  { key: PageKeys.EMPLOYMENT, path: '/h5/ijoin/mobile/postpaid/employment' },
  { key: PageKeys.PREVIEW, path: '/h5/ijoin/new/summary' },
  { key: PageKeys.PAYMENT, path: '/h5/ijoin/payment' },
];
export const pipelinesForFTTH = [
  { key: PageKeys.COVERAGE, path: '/h5/ijoin/ftth/coverage' },
  { key: PageKeys.PLAN, path: '/h5/ijoin/offers' },
  { key: PageKeys.ATTACHMENT, path: '/h5/ijoin/attachment' },
  { key: PageKeys.PROFILE, path: '/h5/ijoin/profile' },
  { key: PageKeys.CONTACT, path: '/h5/ijoin/contact' },
  { key: PageKeys.EMPLOYMENT, path: '/h5/ijoin/employment' },
  { key: PageKeys.ADDRESS, path: '/h5/ijoin/ftth/address' },
  { key: PageKeys.PREVIEW, path: '/h5/ijoin/preview' },
  { key: PageKeys.PAYMENT, path: '/h5/ijoin/payment' },
];
export const pipelinesForFWA = [
  { key: PageKeys.HOME, path: '/h5/ijoin/fwa' },
  { key: PageKeys.COVERAGE, path: '/h5/ijoin/fwa/address' },
  { key: PageKeys.PLAN, path: '/h5/ijoin/offers' },
  { key: PageKeys.ATTACHMENT, path: '/h5/ijoin/attachment' },
  { key: PageKeys.PROFILE, path: '/h5/ijoin/profile' },
  { key: PageKeys.CONTACT, path: '/h5/ijoin/contact' },
  { key: PageKeys.EMPLOYMENT, path: '/h5/ijoin/employment' },
  { key: PageKeys.PREVIEW, path: '/h5/ijoin/preview' },
];
export const pipelinesForFWAV2 = [
  { key: PageKeys.HOME, path: '/h5/ijoin/fwa' },
  { key: PageKeys.PLAN, path: '/h5/ijoin/fwa/plan' },
  { key: PageKeys.INVITATION, path: '/h5/ijoin/invitation' },
  { key: PageKeys.COVERAGE, path: '/h5/ijoin/new/coverage' },
  { key: PageKeys.PROFILE, path: '/h5/ijoin/new/profile' },
  { key: PageKeys.EMPLOYMENT, path: '/h5/ijoin/new/employment' },
  { key: PageKeys.INSTALLATION, path: '/h5/ijoin/new/installation' },
  { key: PageKeys.PREVIEW, path: '/h5/ijoin/new/summary' },
  { key: PageKeys.PAYMENT, path: '/h5/ijoin/payment' },
];

export const getPagePath = (pageKey, pipeline) => {
  const page = pipeline?.find(p => p.key === pageKey);
  return page?.path;
};

// 各种场景下流程页面集合
export const Pipelines = {
  [Business.POSTPAID]: pipelines,
  [Business.FTTH]: pipelinesForFTTH,
  [Business.FWA_POSTPAID]: pipelinesForFWA,
};
export const PipelinesV2 = {
  [Business.POSTPAID]: pipelinesV2,
  [Business.FWA_POSTPAID]: pipelinesForFWAV2,
};

const getLastPage = (cur, business = Business.POSTPAID) => {
  if (!cur) {
    return null;
  }

  const pages = Pipelines[business];
  const curIdx = pages?.findIndex(page => page.key === cur);
  if (curIdx <= 0) {
    return null;
  }
  return pages[curIdx - 1];
};

export const getHomePage = (business = Business.POSTPAID) => Pipelines[business][0]?.path;

export const commonBack = (currentPage, business = Business.POSTPAID, query = {}) => {
  const lastPage = getLastPage(currentPage, business);
  if (lastPage?.path) {
    history.replace(
      withBusiness(
        {
          pathname: lastPage.path,
          query,
        },
        business,
      ),
    );
  }
};

export const commonBackV2 = (currentPage, business = Business.POSTPAID, query = {}) => {
  const pages = PipelinesV2[business];
  const curIdx = pages?.findIndex(page => page.key === currentPage);
  if (curIdx <= 0) return;
  const lastPage = pages[curIdx - 1];
  if (lastPage?.path) {
    history.replace(
      withBusiness(
        {
          pathname: lastPage.path,
          query,
        },
        business,
      ),
    );
  }
};

export const backPage = (pageKey, pipeline, business, query = {}) => {
  const curIdx = pipeline?.findIndex(page => page.key === pageKey);
  if (curIdx <= 0) return;
  const lastPage = pipeline?.[curIdx - 1];
  if (lastPage?.path) {
    history.replace(withBusiness({ pathname: lastPage.path, query }, business));
  }
};

export const IDTypeOptions = [
  [
    { label: 'Passport', value: '1' },
    { label: 'Driver’s License', value: '2' },
    { label: 'Philippine Identification', value: '40' },
    { label: 'Social Security System ID', value: '41' },
    // { label: 'Bureau of Internal Revenue ID', value: '42' },
    // { label: 'Firearms’ License to Own and Possess ID', value: '29' },
    // { label: 'Government Service Insurance System e-Card', value: '5' },
    // { label: 'Integrated Bar of the Philippines ID', value: '9' },
    // { label: 'National Bureau of Investigation clearance', value: '21' },
    { label: 'Other valid government-issued ID with photo', value: '1561' },
    // { label: 'Overseas Workers Welfare Administration ID', value: '25' },
    // { label: 'Person with Disabilities card', value: '43' },
    // { label: 'Police clearance', value: '28' },
    // { label: 'Professional Regulation Commission ID', value: '3' },
    // { label: 'Senior Citizen’s card', value: '33' },
    // { label: 'Unified Multi-purpose Identification Card', value: '7' },
    // { label: 'Voter’s ID', value: '4' },
  ],
];

export const SexOptions = [
  [
    { label: 'Male', value: '1', key: '1' },
    { label: 'Female', value: '2', key: '2' },
    { label: 'Others', value: '4', key: '4' },
    // { label: 'Non-Binary', value: '4', key: '4' },
  ],
];

export const DeliveryMethod = {
  DELIVERY: 'A',
  INSTALLATION: 'D',
};

export const nameFilter = (val, retainDigit = false) =>
  val?.replaceAll(retainDigit ? /^\s+|[^A-Za-z0-9\s]/g : /^\s+|[^A-Za-z\s]/g, '').replaceAll(/\s+/g, ' ');
export const normalizeFormData = (values = {}, retainDigit = false) => {
  const reg = retainDigit ? /^\s+|[^A-Za-z0-9\s]|\s+$/g : /^\s+|[^A-Za-z\s]|\s+$/g;
  const fileds = ['firstName', 'middleName', 'lastName', 'custName', 'employerName'];
  const normalized = { ...values };
  fileds.forEach(filed => {
    if (filed in normalized) {
      const origin = normalized[filed];
      normalized[filed] = origin?.replaceAll(reg, '').replaceAll(/\s+/g, ' ');
    }
  });
  return normalized;
};

/**
 * @typedef {{
 *   label: string,
 *   name: string,
 *   type: string,
 *   placeholder?: string,
 *   disabled?: boolean,
 *   readOnly?: boolean,
 *   required?: boolean,
 *   requiredMessage?: boolean,
 *   options?: any[][],
 *   maxLength?: number,
 *   inputType?: string,
 *   formatter?: string,
 *   displayFormatter?: string,
 *   range?: [string, string]
 *   [k: string]: any,
 * }} Field
 * @type {Field[]}
 */
export const ProfileFormFields = [
  {
    label: 'Prefix/Suffix',
    name: 'salutation',
    type: FORM_ITEM_TYPE.SELECT,
    placeholder: 'Please select',
  },
  {
    name: 'certType',
    required: true,
    label: 'ID Type',
    type: FORM_ITEM_TYPE.SELECT,
    options: IDTypeOptions,
    placeholder: 'Please select',
    readOnly: true,
    suffix: null,
    requiredMessage: 'Please select',
  },
  {
    name: 'certNbr',
    placeholder: 'Please input',
    required: true,
    requiredMessage: 'Please input',
    label: 'ID Number',
    maxLength: 100,
    type: FORM_ITEM_TYPE.INPUT,
    inputType: 'text',
  },
  {
    name: 'firstName',
    placeholder: 'Please input',
    required: true,
    requiredMessage: 'Please input',
    label: 'First Name',
    maxLength: 120,
    type: FORM_ITEM_TYPE.INPUT,
    filter: nameFilter,
    inputType: 'text',
  },
  {
    name: 'middleName',
    placeholder: 'Please input',
    requiredMessage: 'Please input',
    label: 'Middle Name',
    maxLength: 120,
    type: FORM_ITEM_TYPE.INPUT,
    filter: nameFilter,
    inputType: 'text',
  },
  {
    name: 'lastName',
    placeholder: 'Please input',
    required: true,
    requiredMessage: 'Please input',
    label: 'Last Name',
    maxLength: 120,
    type: FORM_ITEM_TYPE.INPUT,
    filter: nameFilter,
    inputType: 'text',
  },
  {
    name: 'gender',
    placeholder: 'Please select',
    label: 'Gender',
    type: FORM_ITEM_TYPE.SELECT,
    options: SexOptions,
    required: true,
    requiredMessage: 'Please select',
  },
  {
    name: 'birthday',
    label: 'Birthdate',
    type: FORM_ITEM_TYPE.DATE_PICKER,
    displayFormatter: VOUCHER_FORMAT,
    range: ['1900-01-01', moment().subtract(18, 'year').format(PARAM_FORMAT)],
    defaultSelected: moment().subtract(18, 'year').format(PARAM_FORMAT),
    required: true,
    requiredMessage: 'Please select',
    placeholder: 'Please select',
  },
];

export const PersonalDetailsFormFields = [
  {
    label: 'Prefix/Suffix',
    name: 'salutation',
    type: FORM_ITEM_TYPE.SELECT,
    placeholder: 'Please select',
  },
  {
    name: 'certType',
    required: true,
    label: 'ID Type',
    type: FORM_ITEM_TYPE.SELECT,
    options: IDTypeOptions,
    placeholder: 'Please select',
    disabled: true,
    suffix: null,
    requiredMessage: 'Please select',
  },
  {
    name: 'certNbr',
    placeholder: 'Please input',
    required: true,
    requiredMessage: 'Please input',
    label: 'ID Number',
    maxLength: 100,
    type: FORM_ITEM_TYPE.INPUT,
    inputType: 'text',
  },
  {
    name: 'firstName',
    placeholder: 'Please input',
    required: true,
    requiredMessage: 'Please input',
    label: 'First Name',
    maxLength: 120,
    type: FORM_ITEM_TYPE.INPUT,
    filter: nameFilter,
    inputType: 'text',
  },
  {
    name: 'middleName',
    placeholder: 'Please input',
    requiredMessage: 'Please input',
    label: 'Middle Name',
    maxLength: 120,
    type: FORM_ITEM_TYPE.INPUT,
    filter: nameFilter,
    inputType: 'text',
  },
  {
    name: 'lastName',
    placeholder: 'Please input',
    required: true,
    requiredMessage: 'Please input',
    label: 'Last Name',
    maxLength: 120,
    type: FORM_ITEM_TYPE.INPUT,
    filter: nameFilter,
    inputType: 'text',
  },
  {
    name: 'gender',
    placeholder: 'Please select',
    label: 'Gender',
    type: FORM_ITEM_TYPE.SELECT,
    options: SexOptions,
    required: true,
    requiredMessage: 'Please select',
  },
  {
    name: 'birthday',
    label: 'Birth Date',
    type: FORM_ITEM_TYPE.DATE_PICKER,
    displayFormatter: STANDARD_FORMAT,
    suffix: null,
    range: ['1900-01-01', moment().subtract(18, 'year').format(PARAM_FORMAT)],
    defaultSelected: moment().subtract(18, 'year').format(PARAM_FORMAT),
    required: true,
    requiredMessage: 'Please select',
    placeholder: 'Please select',
  },
  {
    name: 'citizenship',
    placeholder: 'Please select',
    required: true,
    requiredMessage: 'Please select',
    label: 'Citizenship',
    type: FORM_ITEM_TYPE.SELECT,
  },
];
export const PersonalDetailsFormFields4FWA = [
  ...PersonalDetailsFormFields,
  {
    name: 'maritalStatus',
    placeholder: 'Please select',
    required: false,
    label: (
      <span>
        Marital Status<span style={{ fontSize: '0.12rem', marginLeft: '0.02rem' }}>(optional)</span>
      </span>
    ),
    type: FORM_ITEM_TYPE.SELECT,
  },
];

// FWA同城邮寄演示需要
export const PersonalDetailsFormFields4FWADemonstration = [
  {
    name: 'fullName',
    placeholder: 'Please input',
    requiredMessage: 'Please input',
    required: true,
    label: 'Full Name',
    maxLength: 50,
    filter: val => nameFilter(val, true),
    type: FORM_ITEM_TYPE.INPUT,
    inputType: 'text',
  },
];

export const PersonalDetailsFormFieldsTaggedESAF = [
  {
    name: 'certNbr',
    placeholder: 'Please input',
    required: true,
    requiredMessage: 'Please input',
    label: 'ID No.',
    maxLength: 100,
    type: FORM_ITEM_TYPE.INPUT,
    inputType: 'text',
  },
  {
    label: 'Prefix',
    name: 'salutation',
    type: FORM_ITEM_TYPE.SELECT,
    placeholder: 'Please select',
  },
  {
    name: 'firstName',
    placeholder: 'Please input',
    required: true,
    requiredMessage: 'Please input',
    label: 'First Name',
    maxLength: 120,
    type: FORM_ITEM_TYPE.INPUT,
    filter: nameFilter,
    inputType: 'text',
  },
  {
    name: 'middleName',
    placeholder: 'Please input',
    requiredMessage: 'Please input',
    label: 'Middle Name',
    maxLength: 120,
    type: FORM_ITEM_TYPE.INPUT,
    filter: nameFilter,
    inputType: 'text',
  },
  {
    name: 'lastName',
    placeholder: 'Please input',
    required: true,
    requiredMessage: 'Please input',
    label: 'Last Name',
    maxLength: 120,
    type: FORM_ITEM_TYPE.INPUT,
    filter: nameFilter,
    inputType: 'text',
  },
  {
    label: 'Suffix',
    name: 'suffix',
    type: FORM_ITEM_TYPE.SELECT,
    placeholder: 'Please select',
  },
  {
    name: 'gender',
    placeholder: 'Please select',
    label: 'Gender',
    type: FORM_ITEM_TYPE.SELECT,
    options: SexOptions,
    required: true,
    requiredMessage: 'Please select',
  },
  {
    name: 'birthday',
    label: 'Date of Birth',
    type: FORM_ITEM_TYPE.DATE_PICKER,
    displayFormatter: STANDARD_FORMAT,
    suffix: null,
    range: ['1900-01-01', moment().subtract(18, 'year').format(PARAM_FORMAT)],
    defaultSelected: moment().subtract(18, 'year').format(PARAM_FORMAT),
    required: true,
    requiredMessage: 'Please select',
    placeholder: 'Please select',
  },
  {
    name: 'citizenship',
    placeholder: 'Please select',
    required: true,
    requiredMessage: 'Please select',
    label: 'Citizenship',
    type: FORM_ITEM_TYPE.SELECT,
  },
  {
    name: 'motherName',
    label: 'Mothers Maiden Name',
    placeholder: 'Please input',
    maxLength: 120,
    type: FORM_ITEM_TYPE.INPUT,
    filter: nameFilter,
    inputType: 'text',
  },
  {
    name: 'tin',
    label: 'TIN ID No.',
    placeholder: 'Please input',
    maxLength: 12,
    type: FORM_ITEM_TYPE.NUMBER_INPUT,
    rules: [
      () => ({
        validator(_, value) {
          let reject = null;
          if (value && value.length !== 12) {
            reject = Promise.reject(new Error('Please fill in digits.'));
          } else {
            reject = Promise.resolve();
          }
          return reject;
        },
      }),
    ],
  },
];

/**
 * @typedef {{
 *   label: string,
 *   name: string,
 *   type: string,
 *   placeholder?: string,
 *   disabled?: boolean,
 *   readOnly?: boolean,
 *   required?: boolean,
 *   requiredMessage?: boolean,
 *   options?: any[][],
 *   maxLength?: number,
 *   inputType?: string,
 *   formatter?: string,
 *   displayFormatter?: string,
 *   range?: [string, string]
 *   [k: string]: any,
 * }} Field
 * @type {Field[]}
 */
export const EmploymentFormFields = [
  {
    label: 'Employer Name',
    name: 'employerName',
    type: FORM_ITEM_TYPE.INPUT,
    required: true,
    maxLength: 60,
    filter: val => nameFilter(val, true),
    requiredMessage: 'Please input',
    placeholder: 'Please input',
  },
  {
    label: 'Occupation',
    name: 'occupation',
    type: FORM_ITEM_TYPE.SELECT,
    required: true,
    requiredMessage: 'Please select',
    placeholder: 'Please select',
  },
  {
    label: 'Employer Address',
    name: 'employerAddr',
    type: FORM_ITEM_TYPE.INPUT,
    maxLength: 255,
    required: true,
    requiredMessage: 'Please input',
    placeholder: 'Please input',
  },
  {
    label: 'Region, Province, City, and Barangay',
    name: 'employerStdAddrId',
    type: FORM_ITEM_TYPE.ADDRESS_PICKER,
    ftthAddress: true,
    required: true,
    requiredMessage: 'Please select',
    placeholder: 'Please select',
  },
  {
    label: 'Employer Contact Number',
    name: 'employerMobile',
    type: FORM_ITEM_TYPE.PHONE,
    required: true,
    maxLength: 11,
    requiredMessage: 'Please input',
    placeholder: 'Please input',
  },
  {
    label: 'Job Title',
    name: 'jobTitle',
    type: FORM_ITEM_TYPE.INPUT,
    maxLength: 30,
    required: true,
    requiredMessage: 'Please input',
    placeholder: 'Please input',
  },
  {
    label: 'Position',
    name: 'position',
    type: FORM_ITEM_TYPE.SELECT,
    required: true,
    requiredMessage: 'Please select',
    placeholder: 'Please select',
  },
];

export const EmploymentFormFieldsV2 = [
  {
    label: 'Occupation',
    name: 'occupation',
    type: FORM_ITEM_TYPE.SELECT,
    required: true,
    requiredMessage: 'Select here',
    placeholder: 'Please select',
  },
  {
    label: 'Company Name',
    name: 'employerName',
    type: FORM_ITEM_TYPE.INPUT,
    required: true,
    maxLength: 60,
    filter: val => nameFilter(val, true),
    requiredMessage: 'Please input',
    placeholder: 'Please input',
  },
  {
    label: 'Job Title',
    name: 'jobTitle',
    type: FORM_ITEM_TYPE.INPUT,
    maxLength: 30,
    required: true,
    requiredMessage: 'Please input',
    placeholder: 'Please input',
  },
  {
    label: 'Position',
    name: 'position',
    type: FORM_ITEM_TYPE.SELECT,
    required: true,
    requiredMessage: 'Select here',
    placeholder: 'Please select',
  },
  {
    label: 'Gross Monthly Income Range',
    name: 'monthlyIncoming',
    type: FORM_ITEM_TYPE.SELECT,
    required: true,
    requiredMessage: 'Select here',
    placeholder: 'Please select',
  },
  {
    label: 'Employer Address',
    name: 'employerAddr',
    type: FORM_ITEM_TYPE.INPUT,
    maxLength: 255,
    required: true,
    requiredMessage: 'Please input',
    placeholder: 'Please input',
  },
  {
    label: 'Region, Province, City, and Barangay',
    name: 'employerStdAddrId',
    type: FORM_ITEM_TYPE.ADDRESS_PICKER,
    required: true,
    requiredMessage: 'Please select',
    placeholder: 'Please select',
  },
  {
    label: 'Employer Contact Number',
    name: 'employerMobile',
    type: FORM_ITEM_TYPE.PHONE,
    required: true,
    maxLength: 12,
    requiredMessage: 'Please input',
    placeholder: 'Please input',
  },
];
export const EmploymentFormFieldsTaggedESAF = [
  {
    label: 'Employment Type',
    name: 'occupation',
    type: FORM_ITEM_TYPE.SELECT,
    required: true,
    requiredMessage: 'Select here',
    placeholder: 'Please select',
  },
  {
    label: 'Employer / Business Name',
    name: 'employerName',
    type: FORM_ITEM_TYPE.INPUT,
    required: true,
    maxLength: 40,
    requiredMessage: 'Please input',
    placeholder: 'Please input',
  },
  {
    label: 'Job Title',
    name: 'jobTitle',
    type: FORM_ITEM_TYPE.INPUT,
    maxLength: 30,
    required: true,
    requiredMessage: 'Please input',
    placeholder: 'Please input',
  },
  {
    label: 'Position Type',
    name: 'position',
    type: FORM_ITEM_TYPE.SELECT,
    required: true,
    requiredMessage: 'Select here',
    placeholder: 'Please select',
  },
  {
    label: 'Gross Monthly Income Range',
    name: 'monthlyIncoming',
    type: FORM_ITEM_TYPE.SELECT,
    required: true,
    requiredMessage: 'Select here',
    placeholder: 'Please select',
  },
  {
    label: 'Floor/Unit No./House No.',
    name: 'employerAddrHouseNo',
    type: FORM_ITEM_TYPE.INPUT,
    maxLength: 29,
    showCount: true,
    required: true,
    requiredMessage: 'Please input',
    placeholder: 'Please input',
  },
  {
    label: 'Street Name/Village Name',
    name: 'employerAddrStreet',
    type: FORM_ITEM_TYPE.INPUT,
    maxLength: 29,
    showCount: true,
    required: true,
    requiredMessage: 'Please input',
    placeholder: 'Please input',
  },
  {
    label: 'Region, Province, City, and Barangay',
    name: 'employerStdAddrId',
    type: FORM_ITEM_TYPE.ADDRESS_PICKER,
    required: true,
    requiredMessage: 'Please select',
    placeholder: 'Please select',
  },
  {
    label: 'Employer Contact Number',
    name: 'employerMobile',
    type: FORM_ITEM_TYPE.PHONE,
    required: true,
    maxLength: 12,
    requiredMessage: 'Please input',
    placeholder: 'Please input',
    rules: [
      () => ({
        validator(_, value) {
          let reject = null;
          if (value && !new RegExp(/^[0-9]{8,12}$/g).test(value)) {
            reject = Promise.reject(new Error('Please fill in digits.'));
          } else {
            reject = Promise.resolve();
          }
          return reject;
        },
      }),
    ],
  },
  {
    label: 'Postal Code',
    name: 'postCode',
    type: FORM_ITEM_TYPE.NUMBER_INPUT,
    maxLength: 4,
    showCount: true,
    required: true,
    requiredMessage: 'Please input',
    placeholder: 'Please input',
    rules: [
      () => ({
        validator(_, value) {
          let reject = null;
          if (value && !new RegExp(/^[0-9]{4}$/g).test(value)) {
            reject = Promise.reject(new Error('Please fill in digits.'));
          } else {
            reject = Promise.resolve();
          }
          return reject;
        },
      }),
    ],
  },
];

// 将动态选项加载到 select field 中
export const formFieldsLoader = (fileds = [], optionsMap = {}) =>
  fileds.map(field => {
    if (field.type === FORM_ITEM_TYPE.SELECT && optionsMap[field.name]) {
      return {
        ...field,
        options: optionsMap[field.name] || [],
      };
    }
    return field;
  });

// 收集必填字段
export const collectRequiredFields = fields => fields?.filter(field => field.required) || [];

const KEY_PERSIST_BUSINESS_DATA = 'ijoin_business_scene_data';
export const persistBusinessData = payload => {
  if (payload?.channelCode === ChannelCode.ESAF) {
    setExtraHeaders({ channel_code: payload.channelCode });
  }
  sessionStorage.setItem(KEY_PERSIST_BUSINESS_DATA, JSON.stringify(payload));
};

export const getBusinessData = () => {
  const data = sessionStorage.getItem(KEY_PERSIST_BUSINESS_DATA);
  return safeJSONParse(data);
};

// ESAF 渠道的请求头要携带 channel_code
(() => {
  const data = getBusinessData();
  if (data?.channelCode === ChannelCode.ESAF) {
    setExtraHeaders({ channel_code: data.channelCode });
  }
})();

export const clearBusinessData = () => sessionStorage.removeItem(KEY_PERSIST_BUSINESS_DATA);
