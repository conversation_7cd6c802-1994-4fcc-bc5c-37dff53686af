import React, { useImper<PERSON><PERSON><PERSON><PERSON>, useState } from 'react';
import { Form, Input } from 'antd';
import { useDispatch } from 'umi';
import { ACC_NBR_DISPLAY_RULE } from '@/constants/config';
import { useConfig, useIntl, useModel, useMount } from '@/hooks';
import { DEFAULT_NUMBER_MAX_LENGTH, DEFAULT_NUMBER_PREFIX } from '@/hooks/useConfig';
import InputNumber from '@/components/InputNumber';
import Icon from '@/components/Icon';
import Block from '../../components/Block';

import iconClear from '@/pages/h5/assets/icon_close_circle_blue.png';
import styles from './index.less';

export default React.forwardRef(({ onValidate }, ref) => {
  const { t } = useIntl();
  const [form] = Form.useForm();
  const dispatch = useDispatch();

  const {
    iJoinFWAInstallationAddress: { receiverName, receiverPhone },
  } = useModel(['iJoinFWAInstallationAddress']);
  const [canClear, setCanClear] = useState(!!receiverPhone);

  const { fixed: numberPrefix = '0', numberLength = 10 } = useConfig(ACC_NBR_DISPLAY_RULE) || {};

  const validateFields = values => {
    let flag = Object.values(values).every(e => e?.length > 0);
    if (flag && values.receiverPhone) {
      flag = values.receiverPhone.length === 10;
    }
    onValidate?.(flag);
  };
  const onValuesChange = (_, values) => {
    validateFields(values);
    setCanClear(!!values.receiverPhone);
  };

  useImperativeHandle(
    ref,
    () => ({
      getFieldsValue() {
        return form.getFieldsValue();
      },
      async saveFieldsValue() {
        const values = await form.getFieldsValue();
        dispatch({
          type: 'iJoinFWAInstallationAddress/saveState',
          payload: {
            receiverName: values.receiverName,
            receiverPhone: values.receiverPhone,
          },
        });
      },
    }),
    [],
  );

  useMount(() => {
    validateFields(form.getFieldValue());
  });

  const handleClear = () => {
    form.setFieldsValue({ receiverPhone: '' });
    onValuesChange({ receiverPhone: '' }, { ...form.getFieldsValue(), receiverPhone: '' });
  };

  const suffix = canClear && <Icon className={styles.iconClear} src={iconClear} size="0.16rem" onClick={handleClear} />;

  return (
    <Block title={t('lb_ijoin_fwa_receiver_details')} className={styles.block}>
      <Form
        layout="vertical"
        form={form}
        className={styles.form}
        initialValues={{ receiverName, receiverPhone }}
        onValuesChange={onValuesChange}
      >
        <Form.Item
          label={t('lb_ijoin_fwa_receiver_name')}
          name="receiverName"
          rules={[{ required: true, message: 'Please input' }]}
        >
          <Input maxLength={100} />
        </Form.Item>
        <Form.Item
          label={t('lb_ijoin_fwa_receiver_number')}
          name="receiverPhone"
          style={{ marginBottom: 0 }}
          rules={[
            { required: true, message: 'Please input' },
            { len: 10, message: 'Invalid number' },
          ]}
        >
          <InputNumber
            className={styles.numberInput}
            maxLength={numberLength || DEFAULT_NUMBER_MAX_LENGTH}
            prefix={numberPrefix || DEFAULT_NUMBER_PREFIX}
            suffix={suffix}
            placeholder={''.padStart(numberLength || DEFAULT_NUMBER_MAX_LENGTH, 'X')}
          />
        </Form.Item>
      </Form>
    </Block>
  );
});
