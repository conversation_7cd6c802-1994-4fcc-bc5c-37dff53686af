import React, { useImperativeHandle } from 'react';
import { Form } from 'antd';
import moment from 'moment';
import { useDispatch } from 'umi';
import { inApp } from '@/utils/bridge';
import { APP_NATIVE_FORMAT, PARAM_FORMAT, STANDARD_FORMAT } from '@/constants/constants';
import { IJOIN_CONFIG } from '@/constants/config';
import { useConfig, useModel, useMount } from '@/hooks';
import Select from '@/pages/h5/components/Select';
import NativeDatePicker, { Refer } from '@/pages/h5/components/NativeDatePicker';
import Block from '../../components/Block';

import styles from './index.less';

export default React.forwardRef(({ onValidate }, ref) => {
  const dispatch = useDispatch();
  const [form] = Form.useForm();

  const { fwaInstallationDate, fwaInstallationOffset = 60 } = useConfig(IJOIN_CONFIG) || {};

  const {
    iJoinFWAInstallationAddress: { appointmentDate, appointmentTime },
  } = useModel(['iJoinFWAInstallationAddress']);

  /**
   * 如果当前时间在fwaInstallationDate":"20240510000000"时间之前，则只能选fwaInstallationDate之后的{fwaInstallationOffset}天之内的时间，
   * 如果当前时间在fwaInstallationDate":"20240510000000"之后（包括等于），只能选从当天往后的{fwaInstallationOffset}天之内的时间
   */
  const today = moment();
  const configDate = moment(fwaInstallationDate, APP_NATIVE_FORMAT);
  const isBeforeConfigDate = fwaInstallationDate && configDate.isValid() && today.isBefore(configDate);
  const range = isBeforeConfigDate
    ? [configDate.clone().add(1, 'd'), configDate.clone().add(fwaInstallationOffset, 'd')]
    : [today.clone().add(1, 'd'), today.clone().add(fwaInstallationOffset, 'd')];

  const validateFields = values => onValidate?.(Object.values(values).every(e => e?.length > 0));

  const onValuesChange = (_, values) => validateFields(values);

  useImperativeHandle(
    ref,
    () => ({
      getFieldsValue() {
        return form.getFieldsValue();
      },
      async saveFieldsValue() {
        const values = await form.getFieldsValue();
        dispatch({
          type: 'iJoinFWAInstallationAddress/saveState',
          payload: {
            appointmentDate: values.appointmentDate,
            appointmentTime: values.appointmentTime,
          },
        });
      },
    }),
    [],
  );

  useMount(() => {
    validateFields(form.getFieldValue());
  });

  const initialValues = { appointmentDate, appointmentTime };

  return (
    <Block title="INSTALLATION DETAILS" className={styles.block}>
      <Form
        layout="vertical"
        form={form}
        className={styles.form}
        initialValues={initialValues}
        onValuesChange={onValuesChange}
      >
        <Form.Item
          label="Preferred Date"
          name="appointmentDate"
          rules={[
            {
              required: true,
              message: 'Please fill in Preferred Date',
            },
          ]}
        >
          <NativeDatePicker
            refer={inApp ? Refer.NATIVE : Refer.SELF}
            defaultValue={today.clone().add(1, 'd').format(PARAM_FORMAT)}
            range={range.map(e => e.format(PARAM_FORMAT))}
            displayFormatter={STANDARD_FORMAT}
            placeholder="Please select"
          />
        </Form.Item>
        <Form.Item
          label="Preferred Time"
          name="appointmentTime"
          style={{ marginBottom: 0 }}
          rules={[
            {
              required: true,
              message: 'Please fill in Preferred Time',
            },
          ]}
        >
          <Select
            placeholder="Please select"
            options={[
              [
                { label: '9:00-11:00', value: '9:00-11:00' },
                { label: '11:00-13:00', value: '11:00-13:00' },
                { label: '13:00-15:00', value: '13:00-15:00' },
                { label: '15:00-17:00', value: '15:00-17:00' },
              ],
            ]}
          />
        </Form.Item>
      </Form>
    </Block>
  );
});
