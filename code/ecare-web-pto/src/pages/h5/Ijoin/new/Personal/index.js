import React, { useEffect, useRef, useState } from 'react';
import { Toast } from 'antd-mobile5';
import classNames from 'classnames';
import moment from 'moment';
import { history, useDispatch } from 'umi';
import { ExclamationCircleFilled } from '@ant-design/icons';
import bridge from '@/utils/bridge';
import { Event, trackSensors } from '@/utils/appsensors';
import { useBoolean, useConfig, useIntl, useLoading, useModel } from '@/hooks';
import { APP_NATIVE_FORMAT, IjoinPlanMenuType, PARAM_FORMAT } from '@/constants/constants';
import { IJOIN_CONFIG } from '@/constants/config';
import H5Layout from '@/layouts/H5Layout';
import { BaseSpin } from '@/components';
import Button from '@/pages/h5/components/Button';
import Modal, { ModalV2 } from '@/pages/h5/components/Modal';
import Block, { TitleBlock } from '../../components/Block';
import { ID_BACK_FILE_KEY, ID_FILE_KEY, SELFIE_KEY } from '../../Attachment';
import { BusinessCodes, HeaderTitle, PageKeys, commonBackV2, getPagePath, pipelinesV2, withBusiness } from '../../data';
import useGuard from '../../data/useGuard';
import useTrackData from '../../data/useTrackData';
import useBusiness from '../../data/useBusiness';
import Address from './Address';
import Attachment from './Attachment';
import Contact from './Contact';
import PersonalDetails from './PersonalDetails';
import CustModal from './CustModal';
import Privacy from './Privacy';

import styles from './index.less';

const NOT_MATCHED_TIP =
  'It seems that the information provided did not match our records. Kindly scan a valid ID with correct information.';

export default function Personal() {
  const [business, { isPostpaid, isFWA, isFWAPrepaid, switchingPre2Post, businessScene }] = useBusiness();
  const guard = useGuard();
  const dispatch = useDispatch();
  const { t } = useIntl();
  const { getSubmittingData } = useTrackData();
  const attachmentRef = useRef();
  const personalFormRef = useRef();
  const addressRef = useRef();
  const contactRef = useRef();
  const [tipVisible, { setTrue: openTipModal, setFalse: closeTipModal }] = useBoolean(false);
  const [custVisible, { setTrue: openCustModal, setFalse: closeCustModal }] = useBoolean(false);
  const [arrearageVisible, { setTrue: openArrearageModal, setFalse: closeArrearageModal }] = useBoolean(false);
  const [ongoingVisible, { setTrue: openOngoingModal }] = useBoolean(false);
  const [tipModalVisible, setTipModalVisible] = useState(false);
  const [isRandomAccNbr, setIsRandomAccNbr] = useState(false);
  const [existingProfile, setExistingProfile] = useState(null);
  const [attachmentReady, setAttachmentReady] = useState(false);
  const [personalDetailsReady, setPersonalDetailsReady] = useState(false);
  const [addressReady, setAddressReady] = useState(false);
  const [contactReady, setContactReady] = useState(false);
  const [privacyReady, setPrivacyReady] = useState(false);

  const loading = useLoading(['iJoinAttachment/compareFaceID', 'iJoinProfile/fetchCustInfo']);
  const checking = useLoading('iJoinProfile/checkCreditScore');

  const {
    oadCheck: { odaAddress },
    iJoinFWAInstallationAddress: { odaResult },
    ijoinPlan: { sourceCode, selectedPlan = {}, planType },
    iJoinAttachment: { tempAttachments, resolvedIDImage, ocrPassed },
    iJoinProfile: { prefixOptions = [], creditScore },
  } = useModel(['oadCheck', 'iJoinFWAInstallationAddress', 'ijoinPlan', 'iJoinAttachment', 'iJoinProfile']);

  const skipODA = switchingPre2Post && planType === IjoinPlanMenuType.SIM_ONLY;
  const odaPassed = (!isPostpaid || odaAddress?.oda === 'N') && (!isFWA || odaResult === 0);
  const canProceed =
    attachmentReady &&
    personalDetailsReady &&
    addressReady &&
    contactReady &&
    privacyReady &&
    ocrPassed &&
    (odaPassed || skipODA);

  const config = useConfig(IJOIN_CONFIG);

  const compareFaceID = (params = {}) =>
    dispatch({
      type: 'iJoinAttachment/compareFaceID',
      payload: {
        cardFile: resolvedIDImage,
        businessType: 2,
        ...params,
      },
    });

  const compare = async params => {
    try {
      const ocrResult = await compareFaceID(params);
      if (!ocrResult) {
        Toast.show(NOT_MATCHED_TIP);
        return false;
      }
    } catch (error) {
      Toast.show(NOT_MATCHED_TIP);
      return false;
    }
    return true;
  };

  const handleAttachmentChange = async (attach, allFiles) => {
    if (SELFIE_KEY in attach || ID_FILE_KEY in attach) {
      // 如果ID文件修改了，需要重置 ocrPassed
      dispatch({
        type: 'iJoinAttachment/saveState',
        payload: {
          ocrPassed: false,
        },
      });
    }

    // 联动 ID Type 字段
    if (attach.IDType?.value) {
      personalFormRef.current?.setFieldsValue({
        certType: attach.IDType.value,
      });
      if (tempAttachments.IDType?.value && attach.IDType?.value !== tempAttachments.IDType?.value) {
        // 重置 ocrPassed
        dispatch({
          type: 'iJoinAttachment/saveState',
          payload: {
            ocrPassed: false,
          },
        });
        // ID type 改动后重置证件照
        dispatch({
          type: 'iJoinAttachment/updateTempAttachments',
          payload: {
            [ID_FILE_KEY]: null,
            [SELFIE_KEY]: null,
          },
        });
        personalFormRef.current?.resetFields();
      }
    }
    // 如果证件或者人脸修改了，需要重新进行人脸比对
    if ((attach[SELFIE_KEY] || attach[ID_FILE_KEY]) && allFiles.selfie?.fileString && allFiles.IDType?.value) {
      const result = await compare({
        identityType: allFiles.IDType?.value,
        selfImage: allFiles.selfie?.fileString,
      });
      dispatch({
        type: 'iJoinAttachment/saveState',
        payload: {
          ocrPassed: result,
        },
      });
      if (result) {
        // 合并 ocr 数据
        const mergedProfile = await dispatch({
          type: 'iJoinAttachment/mergeProfile',
        });
        personalFormRef.current?.resetFields();
        setTimeout(() => {
          personalFormRef.current?.setFieldsValue({
            ...mergedProfile,
            certType: allFiles.IDType?.value,
          });
        }, 0);
      }
    }
  };

  // 获取老客户信息
  const fetchCustInfo = formData =>
    dispatch({
      type: 'iJoinProfile/fetchCustInfo',
      payload: {
        ...formData,
      },
    });

  // 后付费信审
  const audit4Postpaid = (data, submittedData) => {
    const { custOrderId, preApprovalResult, autoApprovalResult } = data || {};
    if (custOrderId) {
      // 自动审核被拒/通过，跳转到查询订单详情
      history.push({
        pathname: '/h5/ijoin/order/detail',
        query: {
          custOrderId: custOrderId || '',
          contactNbr: submittedData?.custInfo?.contactInfo?.primaryContactNbr || '',
          fromProcess: 'Y',
          sourceCode,
        },
      });
      return;
    }
    if (preApprovalResult === 'N' && autoApprovalResult === 'M') {
      // 需要人工审核，进入员工信息采集页面
      history.push(withBusiness({ pathname: '/h5/ijoin/new/employment' }, business));
      return;
    }
    if (preApprovalResult === 'Y') {
      // 不需要审核，进入order summary页面
      history.push(withBusiness({ pathname: '/h5/ijoin/new/summary' }, business));
    }
  };

  // FWA 信审
  const audit4FWA = (data, submittedData) => {
    const { custOrderId, preApprovalResult, autoApprovalResult } = data || {};
    if (custOrderId && preApprovalResult === 'N' && autoApprovalResult === 'D') {
      // 自动审核被拒，跳转到查询订单详情
      history.push({
        pathname: '/h5/ijoin/order/detail',
        query: {
          custOrderId: custOrderId || '',
          contactNbr: submittedData?.custInfo?.contactInfo?.primaryContactNbr || '',
          fromProcess: 'Y',
          sourceCode,
        },
      });
      return;
    }
    if (preApprovalResult === 'Y' || (preApprovalResult === 'N' && autoApprovalResult === 'A')) {
      // 自动审核通过，进入安装时间采集页面
      history.push(withBusiness({ pathname: '/h5/ijoin/new/installation' }, business));
      return;
    }
    if (preApprovalResult === 'N' && autoApprovalResult === 'M') {
      // 需要人工审核，进入员工信息采集页面
      history.push(withBusiness({ pathname: '/h5/ijoin/new/employment' }, business));
    }
  };

  // 预检
  const preCheck = async (usingExistingCustInfo = false) => {
    let payload = getSubmittingData({ withAttachments: true, withSalesInfo: false });
    if (usingExistingCustInfo && existingProfile) {
      const { profile } = await dispatch({
        type: 'iJoinProfile/resolveInfo',
        payload: existingProfile,
        cleanupContact: false,
      });
      // 使用最新的合并后的用户信息
      payload = getSubmittingData({ freshProfile: profile, withAttachments: true, withSalesInfo: false });
      payload.custInfo.custId = existingProfile.custId;
    }
    if (isRandomAccNbr) {
      payload.accNbr = undefined;
    }
    const response = await dispatch({
      type: 'iJoinProfile/checkCreditScore',
      payload,
    });
    if (isPostpaid && response?.oriCode === '40908510') {
      // 号码不可用重新选号码
      setTipModalVisible(true);
      trackSensors(Event.IJOIN_PROCEED_RESULT, {
        entrance: 'PersonalDetails',
        result: 'Fail',
        error_msg: 'We regret to inform you that your chosen mobile number is no longer available.',
      });
      return;
    }
    if (response?.code !== '200') {
      Toast.show(response?.message || 'Please try again later');
      trackSensors(Event.IJOIN_PROCEED_RESULT, {
        entrance: 'PersonalDetails',
        result: 'Fail',
        error_msg: response?.message || 'Please try again later',
      });
      return;
    }
    const data = response?.data;
    if (!data) {
      trackSensors(Event.IJOIN_PROCEED_RESULT, {
        entrance: 'PersonalDetails',
        result: 'Fail',
        error_msg: 'Please try again later',
      });
      return;
    }
    let passed = false;
    switch (data.result) {
      case 0: // 校验成功
      case 1:
        passed = true;
        break;
      case 3: // Suspension欠费校验不通过，弹框提示
        openArrearageModal();
        trackSensors(Event.IJOIN_PROCEED_RESULT, {
          entrance: 'PersonalDetails',
          result: 'Fail',
          error_msg: t('tips_ijoin_outstanding_balance', {
            openAmount: `${creditScore?.creditInfo?.currencySymbol || ''}${
              creditScore?.creditInfo?.displayOpenAmount || ''
            }`,
          }),
        });
        break;
      case 2: // 黑名单校验不通过
      case 4: // 欺诈拆机校验不通过
        trackSensors(Event.IJOIN_PROCEED_RESULT, {
          entrance: 'PersonalDetails',
          result: 'Fail',
          error_msg: `We regret to inform you that your application for ${selectedPlan?.offerName} could not be approved at this time. Thank you`,
        });
        history.push(
          withBusiness(
            {
              pathname: '/h5/ijoin/oops',
              state: {
                offerName: selectedPlan?.offerName,
              },
            },
            business,
          ),
        );
        break;
      case 5: // 有在途单，弹框提示
        openOngoingModal();
        trackSensors(Event.IJOIN_PROCEED_RESULT, {
          entrance: 'PersonalDetails',
          result: 'Fail',
          error_msg: 'You seem to have a pending application.',
        });
        break;
      case 6: // 预付费校验通过，进入安装时间采集页面
        if (isFWA) {
          trackSensors(Event.IJOIN_PROCEED_RESULT, {
            entrance: 'PersonalDetails',
            result: 'Success',
          });
          history.push(withBusiness({ pathname: '/h5/ijoin/new/installation' }, business));
        }
        break;
      default:
        break;
    }
    if (!passed) return;

    trackSensors(Event.IJOIN_PROCEED_RESULT, {
      entrance: 'PersonalDetails',
      result: 'Success',
    });
    if (isFWA) {
      audit4FWA(data, payload);
      return;
    }
    audit4Postpaid(data, payload);
  };

  const checkCustInfoExist = async () => {
    closeTipModal();
    const detail = (await personalFormRef.current?.getFieldsValue()) || {};
    const custInfo = await fetchCustInfo({
      ...detail,
      birthday: detail.birthday ? moment(detail.birthday).format(APP_NATIVE_FORMAT) : null,
      offerNbr: selectedPlan?.offerNbr,
      businessScene: BusinessCodes[business],
    });
    if (custInfo) {
      openCustModal();
      setExistingProfile({
        ...custInfo,
        birthday: custInfo.birthday ? moment(custInfo.birthday, APP_NATIVE_FORMAT).format(PARAM_FORMAT) : null,
      });
      return;
    }
    preCheck();
  };

  const handleChooseAnotherNumber = () => {
    setTipModalVisible(false);
    history.replace(
      withBusiness({ pathname: '/h5/ijoin/number', query: { refresh: 'Y', from: PageKeys.PROFILE } }, business),
    );
  };

  const handleRandomNumber = () => {
    setIsRandomAccNbr(true);
    setTipModalVisible(false);
  };

  const trackEventSA = () => {
    const payload = { sourceCode, entrance: 'PersonalDetails' };
    trackSensors(Event.IJOIN_PROCEED_CLICK, payload);
  };

  const saveData = () => {
    [attachmentRef, personalFormRef, addressRef, contactRef].forEach(ref => ref.current?.saveFieldsValue?.());
  };

  const handleProceed = () => {
    openTipModal();
    saveData();
    trackEventSA();
    // history.push(withBusiness({ pathname: '/h5/ijoin/new/summary' }, business));
  };

  useEffect(() => {
    if (isRandomAccNbr) {
      preCheck(custVisible && existingProfile);
    }
  }, [isRandomAccNbr]);

  const bannerRender = () => {
    // FWA 预付费
    if (isFWAPrepaid) {
      return (
        <>
          <p>Your DITO 5G Home WiFi Prepaid will be automatically registered upon activation.</p>
          <p style={{ marginTop: '0.24rem' }}>
            To complete this process, kindly attach clear copies of the required documents.
          </p>
        </>
      );
    }
    // FWA 后付费
    if (isFWA) {
      return (
        <>
          <p>Your DITO 5G Home WiFi Postpaid will be automatically registered upon activation.</p>
          <p style={{ marginTop: '0.24rem' }}>
            To complete this process, kindly attach clear copies of the required documents.
          </p>
        </>
      );
    }
    // 后付费
    return (
      <>
        <p>Your DITO Mobile Postpaid will be automatically registered upon activation.</p>
        <p style={{ marginTop: '0.24rem' }}>
          To complete this process, kindly attach clear copies of the required documents.
        </p>
      </>
    );
  };

  const addressDescription = () => {
    // for mobile postpaid
    let desc = 'Please ensure to provide accurate details. Your address will be our basis for delivery and billing.';
    if (isFWAPrepaid) {
      // for fwa prepaid
      desc = 'Please ensure to provide accurate details. Your address will be our basis for installation/delivery.';
    } else if (isFWA) {
      // for fwa postpaid
      desc =
        'Please ensure to provide accurate details. Your address will be our basis for installation/delivery and billing.';
    }
    return (
      <div className={styles.addressDescription}>
        <p style={{ marginBottom: '0.16rem' }}>
          <span className={styles.highlight}>IMPORTANT:</span> <span>{desc}</span>
        </p>
      </div>
    );
  };

  return (
    <BaseSpin spinning={loading || (checking && !custVisible)}>
      <H5Layout
        guard={guard}
        background="#F7F7F7"
        headerProps={{
          title: HeaderTitle[business],
          titleAlign: 'left',
          background: 'white',
          className: styles.header,
          round: false,
          onBack() {
            if (isPostpaid && switchingPre2Post) {
              history.replace(
                withBusiness(
                  {
                    pathname: getPagePath(PageKeys.PLAN, pipelinesV2),
                    query: {
                      jumpType: planType === IjoinPlanMenuType.WITH_HANDSET ? 'handset' : 'plan',
                    },
                  },
                  business,
                ),
              );
              return;
            }
            commonBackV2.call(null, PageKeys.PROFILE, business);
          },
        }}
      >
        <TitleBlock title="Personal Details">{bannerRender()}</TitleBlock>
        <main className={styles.main}>
          <Block title="UPLOAD ID" className={styles.block}>
            <Attachment
              ref={attachmentRef}
              includes={isFWA ? [ID_FILE_KEY, ID_BACK_FILE_KEY, SELFIE_KEY] : [ID_FILE_KEY, SELFIE_KEY]}
              onValuesChange={handleAttachmentChange}
              onValidate={setAttachmentReady}
            />
          </Block>
          <Block title="PERSONAL DETAILS" className={styles.block}>
            <PersonalDetails
              ref={personalFormRef}
              disabled={ocrPassed === false}
              onValidate={setPersonalDetailsReady}
            />
          </Block>
          <Block
            title="ADDRESS"
            description={addressDescription()}
            className={classNames(styles.block, styles.address)}
          >
            <Address ref={addressRef} onValidate={setAddressReady} />
          </Block>
          <Block title="CONTACT DETAILS" className={styles.block}>
            <Contact ref={contactRef} onValidate={setContactReady} />
          </Block>
          <Block className={styles.block}>
            <Privacy onValidate={setPrivacyReady} beforeViewPrivacy={saveData} />
          </Block>
          <Button disabled={!canProceed} onClick={handleProceed}>
            Proceed
          </Button>
        </main>

        <ModalV2
          visible={tipVisible}
          title="Important"
          okText="I understand"
          cancelText="Go back"
          onOk={() => {
            if (switchingPre2Post) {
              // 预转后直接进行预校验
              closeTipModal();
              preCheck();
              return;
            }
            checkCustInfoExist();
          }}
          onCancel={closeTipModal}
        >
          <p style={{ textAlign: 'center' }}>
            Please confirm you have attached valid and clear copy of the required documents to help us with the
            application review.
          </p>
        </ModalV2>
        <Modal
          centered
          title={(
            <div>
              <ExclamationCircleFilled style={{ color: '#0038A7', fontSize: '0.32rem', marginBottom: '0.16rem' }} />
              <div style={{ fontSize: '0.2rem' }}>Oops!</div>
            </div>
          )}
          className={styles.tipModal}
          maskClosable={false}
          open={tipModalVisible}
          visible={tipModalVisible}
          okText="I will choose another number"
          onOk={handleChooseAnotherNumber}
          cancelText="Assign me a random number"
          onCancel={handleRandomNumber}
        >
          <p>We regret to inform you that your chosen mobile number is no longer available.</p>
        </Modal>
        <CustModal
          prefixOptions={prefixOptions}
          custInfo={existingProfile}
          visible={custVisible}
          onClose={closeCustModal}
          onCheck={usingExistingCustInfo => {
            closeCustModal();
            preCheck(usingExistingCustInfo);
          }}
        />
        <ModalV2
          visible={arrearageVisible}
          title="Oops!"
          type="confirm"
          footer={{ layout: 'vertical' }}
          okText={t('btn_ijoin_pay_outstanding')}
          cancelText={t('btn_ijoin_pay_outstanding_close')}
          onOk={() => bridge.faqDetail({ fdId: config?.outstandingFaqId, docSubject: config?.outstandingFaqName })}
          onCancel={closeArrearageModal}
        >
          <div style={{ textAlign: 'center' }}>
            <p style={{ marginBottom: '0.24rem' }}>
              {t('tips_ijoin_outstanding_balance', {
                openAmount: `${creditScore?.creditInfo?.currencySymbol || ''}${
                  creditScore?.creditInfo?.displayOpenAmount || ''
                }`,
              })}
            </p>
          </div>
        </ModalV2>
        <ModalV2
          visible={ongoingVisible}
          title="Oops!"
          type="confirm"
          okText="Track ongoing application"
          cancelText={false}
          onOk={() => {
            if (config?.qryOrderUrl) {
              window.location.href = config.qryOrderUrl;
            }
          }}
        >
          <p style={{ textAlign: 'center' }}>You seem to have a pending application.</p>
        </ModalV2>
      </H5Layout>
    </BaseSpin>
  );
}
