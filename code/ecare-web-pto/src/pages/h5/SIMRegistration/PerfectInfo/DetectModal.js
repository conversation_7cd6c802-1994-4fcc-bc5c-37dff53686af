import React, { useEffect, useState } from 'react';
import MobilePhone from '@/components/Icon/MobilePhone';
import { ModalV2 } from '../../components/Modal';

import styles from './index.less';

export default function DetectModal({ visible, prefix, options = [], onOk, onCancel }) {
  const [selected, setSelected] = useState(options?.[0]);

  useEffect(() => {
    if (options?.length > 0 && visible) {
      setSelected(options[0]);
    }
  }, [options, visible]);

  return (
    <ModalV2
      visible={visible}
      open={visible}
      className={styles.detectModal}
      title="Mobile Number Detected"
      footer={{ layout: 'vertical' }}
      okText="Proceed"
      cancelText="Enter mobile number manually"
      onOk={() => onOk?.(selected)}
      onCancel={onCancel}
    >
      <ul className={styles.options}>
        {options.map(item => (
          <li key={item} className={selected === item ? styles.active : ''} onClick={() => setSelected(item)}>
            <MobilePhone style={{ marginRight: '0.08rem', color: '#CE1126', fontSize: '0.24rem' }} />
            <span>
              {prefix || ''}
              {item}
            </span>
          </li>
        ))}
      </ul>
    </ModalV2>
  );
}
