.changeEmail {
  min-width: 750px;
  max-width: 750px;
  padding: 0;
  top: 150px;
  .AmountInputForm {
    padding-bottom: 16px;
  }
  .EmailCode{
    padding-bottom: 26px;
  }
  :global {
    .ant-form.ant-form-horizontal .ant-btn.ant-btn-text {
      background: #fff;
      padding: 0;
      height: 19px;
      border: none;
      color: #0038a8;
    }
    .ant-form.ant-form-horizontal .ant-btn.ant-btn-text:hover {
      background: #fff;
      height: 19px;
      border: none;
      color: #0038a8;
    }
    .ant-modal-content .ant-modal-body .ant-form.ant-form-horizontal .ant-btn[disabled] {
      color: #ccc;
    }
    .ant-modal-content .ant-modal-body .ant-form.ant-form-horizontal .ant-btn[disabled]:active {
      //
      color: #ccc;
    }
    .ant-modal-root .ant-btn,
    .ant-modal-root .ant-btn:hover,
    .ant-modal-root .ant-btn:focus {
      min-width: 120px;
      height: 19px;
      padding: 0;
      font-size: 16px;
      font-family: Montserrat;
      font-weight: 500;
      line-height: 19px;
      color: #606060;
      background: #ffffff;
      border: none;
    }
    .ant-row.ant-form-item {
      margin-bottom: 5px;
    }
    .ant-modal-content {
      background: #ffffff;
      box-shadow: 0px 3px 6px rgba(0, 0, 0, 0.16);

      border-radius: 25px;
      .ant-modal-body {
        padding-top: 0;
      }
    }
    .ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before,
    .ant-form-item-label > label::after {
      content: '';
    }
    .ant-row {
      display: block;
      // margin: 20px 0;
    }
    .ant-modal-content .ant-modal-body {
      // padding: 0;
    }
    .ant-modal-root .ant-modal-content .ant-modal-body .ant-form-item {
      margin-bottom: 0 !important;
    }
    .baseWrap .ant-form-item,
    .ant-modal-root .ant-form-item {
      margin-bottom: 4px;
    }
    .ant-form-item-label > label {
      font-size: 16px;
      font-family: Montserrat;
      font-weight: 500;
      line-height: 19px;
      color: #2f3043;
    }
    .ant-input {
      min-width: 293px;
      max-width: 293px;
      height: 40px;
      background: #fff;
      border: 1px solid #cecfdb;
      border-radius: 5px;
      font-family: Montserrat;
      font-weight: 500;
      font-size: 16px;
      color: #2f3043;
    }
    .ant-input::placeholder {
      font-size: 16px;
      font-family: Montserrat;
      font-weight: 400;
      line-height: 19px;
      color: #cccccc;
    }
    .ant-form-item-explain.ant-form-item-explain-error {
      font-size: 12px;
      font-family: Montserrat;
      font-weight: 500;
      line-height: 15px;
      color: #f41b34;
      width: 293px;
      height: 3px;
    }
    .ant-form-item-has-error .ant-input,
    .ant-form-item-has-error .ant-input-affix-wrapper,
    .ant-form-item-has-error .ant-input:hover,
    .ant-form-item-has-error .ant-input-affix-wrapper:hover {
      background-color: #fff1f2 !important;
      border-color: #ff4d4f !important;
    }
    .ant-input:focus,
    .ant-input-focused {
      box-shadow: none;
    }
    .ant-modal-footer {
      text-align: left;
    }
  }
  .SMSInput {
    background-color: #eee;
  }
  .SMSInputTrue {
    background-color: #fff;
  }
  .btn {
    margin-top: 65px;
  }
  .title {
    height: 25px;
    font-size: 21px;
    font-family: 'Exo 2';
    font-weight: 500;
    line-height: 25px;
    color: #2f3043;
    margin-bottom: 10px;
  }
  .titleInfo {
    width: 426px;
    font-size: 16px;
    font-family: Montserrat;
    font-weight: 500;
    line-height: 24px;
    color: #2f3043;
    padding-bottom: 20px;
  }
  .tips {
    font-size: 16px;
    font-family: Montserrat;
    font-weight: 500;
    line-height: 24px;
    color: #2f3043;
  }
  .countdown {
    font-size: 16px;
    font-family: Montserrat;
    font-weight: 400;
    line-height: 19px;
    color: #777777;
  }
  .BrandElement {
    width: 200px;
    height: 110px;
    position: absolute;
    top: 180px;
    right: 80px;
  }
  .ResendBtn {
    border: none;
    padding: 0 0 0 4px;
    padding: 0;
    height: 19px;
    font-size: 16px;
    font-family: Montserrat;
    font-weight: 500;
    line-height: 19px;
    color: #cccccc;
    background: #fff;
    // padding-left: 4px;
  }
  .ResendBtnAct {
    display: flex;
    align-items: center;
    // padding: 0 0 0 4px;
    border: none;
    height: 19px;
    font-size: 16px;
    font-family: Montserrat;
    line-height: 19px;
    color: #0038a8;
    // font-weight: 600;
    // padding-left: 4px;
    background: #fff;
    span {
      // margin-right: 4px;
      background: #fff;
    }
  }
  .submitBtn {
    height: 40px;
    padding: 0 40px;
    background: linear-gradient(90deg, #e7090d 0%, #f75814 100%);
    border-radius: 20px;
    font-size: 16px;
    font-family: Montserrat;
    font-weight: 500;
    line-height: 16px;
    color: #ffffff;
    margin-right: 20px;
  }
  .CloseBtn {
    height: 40px;
    padding: 0 40px;
    background: #ffffff;
    border: 2px solid #cccccc;

    border-radius: 20px;
    font-size: 16px;
    font-family: Montserrat;
    font-weight: 500;
    line-height: 19px;
    color: #606060;
  }
}
