import React, { PureComponent } from 'react';
import { Modal, Button, Form, Input } from 'antd';
import { connect } from 'dva';
import { get } from 'lodash';
import { BrandElement } from '@/constants/enumeration/getPicture';
import { getUserInfo } from '@/utils/utils';
import styles from './index.less';

@connect(() => ({}))
class index extends PureComponent {
  formRef = React.createRef();

  state = {
    btnActive: true, // 发送验证码之后才可以点击submit
    codeDis: true,
    showGet: true, // 展示哪一个按钮
    countDown: 10,
    codeNumber: false, // 第一次未点击验证码
    emailAddress: '', // 赞成你当前页面的email
    code: 1,
    codeActive: true,
    modalLoading: false,
    codeDisable: true,
  };

  componentDidMount = () => {
    if (document.getElementById('code')) {
      document.getElementById('code').style.backgroundColor = '#eee';
    }
  };

  onFinish = () => {
    const { dispatch, profileInfo } = this.props;
    const userInfo = getUserInfo();
    const { validateFields, setFields } = this.formRef.current;
    validateFields().then(values => {
      dispatch({
        type: 'profile/modUserInfo', // 发送更改请求
        payload: {
          userId: get(userInfo, 'userId'),
          subsId: get(userInfo, 'subsId'),
          custId: get(userInfo, 'custId'),
          acctId: get(userInfo, 'defaultAcctId'),
          birthday: get(profileInfo, 'birthday') || null,
          ditoNumber: get(profileInfo, 'ditoNumber'),
          firstName: get(profileInfo, 'firstName'),
          middleName: get(profileInfo, 'middleName'),
          lastName: get(profileInfo, 'lastName'),
          gender: get(profileInfo, 'gender'),
          otherContactNumber: get(profileInfo, 'otherContactNumber'),
          email: values.emailAddress,
          captcha: values.EmailCode,
        },
      }).then(res => {
        // === 'USER-CENTER-SERVER-00008'
        if (res.code) {
          // 本页提示
          this.setState({
            modalLoading: false,
          });
          setFields([
            {
              name: 'EmailCode',
              value: values.EmailCode,
              errors: [get(res, 'message') || 'You have entered an invalid PIN.'],
            },
          ]);
        } else {
          // 失败
          this.props.changeEmailfaid();
          this.setState({
            modalLoading: false,
          });
          this.setState({
            showGet: true,
            codeNumber: false,
            codeDis: true,
            btnActive: true,
            codeActive: true,
            modalLoading: false,
          });
          this.formRef.current.resetFields();
        }
        if (!res.code) {
          // 成功
          this.props.onSubmit(values);
          this.setState({
            modalLoading: false,
          });
          this.formRef.current.resetFields();
        }
      });
    });
  };

  handeEmail = e => {
    this.setState({
      codeDisable: false,
    });
    const reg = /^[A-Za-z0-9:.-_]+[@][a-zA-Z0-9._-]+(\.[a-zA-Z0-9]+)+$/;
    if (!reg.test(e.target.value)) {
      // 按钮不可用
      this.setState({
        btnActive: true,
        codeDis: true,
      });
    } else {
      // 按钮可以
      this.setState({
        btnActive: false,
        codeDis: false,
      });
      document.getElementById('code').style.backgroundColor = '#fff';
    }
  };

  //  codeblur事件
  blur = e => {
    if (e.target.value.length < 6) {
      this.setState({
        codeActive: true,
      });
    }
  };

  handeCode = e => {
    const reg = /^([0-9]{1,6})$/;
    if (!reg.test(e.target.value)) {
      this.setState({
        codeActive: true,
      });
    }

    if (e.target.value.length === 6) {
      this.setState({
        codeActive: false,
      });
    } else {
      this.setState({
        codeActive: true,
      });
    }
  };

  // 倒计时
  getSMSCode = () => {
    const userInfo = getUserInfo();
    const { dispatch, profileInfo } = this.props;
    const { emailAddress } = this.state;
    dispatch({
      type: 'profile/getCode',
      payload: {
        custId: get(userInfo, 'custId'),
        custName: `${get(profileInfo, 'firstName')} ${get(profileInfo, 'middleName')} ${get(profileInfo, 'lastName')}`,
        sendTo: emailAddress,
      },
    });
    // 发送请求
    this.setState(
      {
        countDown: 120,
        showGet: false,
      },
      () => {
        this.timer = setInterval(() => {
          const { countDown } = this.state;
          this.setState({ countDown: countDown - 1 < 0 ? 0 : countDown - 1 }, () => {
            if (countDown <= 0) {
              clearInterval(this.timer);
              this.setState({
                showGet: true,
                codeNumber: true,
              });
            }
          });
        }, 1000);
      },
    );
  };

  // form表单改变事件
  valueChange = value => {
    if (value.emailAddress) {
      this.setState({
        emailAddress: value.emailAddress,
      });
    } else if (value.EmailCode) {
      // 替换输入的其他字符;
      const numberValue = value.EmailCode.replace(/[^\d]/g, '');
      this.formRef.current.setFieldsValue({
        EmailCode: numberValue,
      });
    }
  };

  // 关闭
  handelClose = () => {
    this.formRef.current.resetFields();
    // loading：false ,code框输入提示置空白
    this.setState({
      showGet: true,
      codeNumber: false,
      codeDis: true,
      btnActive: true,
      codeActive: true,
      modalLoading: false,
      codeDisable: true,
    });
    clearInterval(this.timer);
    this.props.handelClose();
  };

  submit = () => {
    this.setState({
      modalLoading: true,
    });
  };

  render() {
    const { changeEmailModal } = this.props;
    const {
      btnActive,
      codeDis,
      countDown,
      showGet,
      codeNumber,
      code,
      emailValue,
      codeActive,
      modalLoading,
      codeDisable,
    } = this.state;

    return (
      <div>
        <Modal className={styles.changeEmail} open={changeEmailModal} visible={changeEmailModal} footer={[]}>
          <p className={styles.title}>Change Email Address</p>
          <p className={styles.titleInfo}>
            {/* Please input your new Email Address. Email Code will be sent to your current Email Address.  */}
            We will be sending an OTP to your new email address upon clicking{' '}
            <span style={{ fontWeight: 600 }}>Get Email Code</span>.
          </p>

          <Form ref={this.formRef} onFinish={this.onFinish} onValuesChange={this.valueChange} requiredMark={false}>
            <Form.Item
              label="Enter your New Email Address"
              className={styles.AmountInputForm}
              name="emailAddress"
              rules={[
                {
                  required: true,
                  message: 'Please enter a valid Email Address.',
                },
                {
                  pattern: /^[A-Za-z0-9:.-_]+[@][a-zA-Z0-9._-]+(\.[a-zA-Z0-9]+)+$/,
                  message: 'Please enter a valid Email Address.',
                },
              ]}
            >
              <Input
                placeholder="New Email Address"
                className={styles.EmailAddress}
                onChange={this.handeEmail}
                value={emailValue}
                disabled={!!modalLoading}
                autoComplete="off"
              />
            </Form.Item>
            {/* eslint-disable-next-line no-nested-ternary */}
            {showGet ? (
              codeNumber ? (
                <Button type="text" className={styles.ResendBtnAct} onClick={this.getSMSCode} disabled={codeDis}>
                  <span className="icon-refresh" style={{ fontSize: '22px' }} />
                  <span>Resend OTP</span>
                </Button>
              ) : (
                <Button type="text" className={styles.ResendBtnAct} onClick={this.getSMSCode} disabled={codeDis}>
                  Get Email Code
                </Button>
              )
            ) : (
              <p className={styles.countdown}>Resend in {countDown} seconds</p>
            )}
            <Form.Item
              label="Enter Email Code"
              className={`${styles.AmountInputForm}${styles.EmailCode}`}
              name="EmailCode"
              rules={[
                {
                  required: true,
                  message: 'Please enter a valid PIN.',
                },
                {
                  len: 6,
                  message: 'Please enter a valid PIN.',
                },
                {
                  pattern: /^([0-9]{1,6})$/,
                  message: 'You have entered an invalid PIN.',
                },
              ]}
            >
              <Input
                placeholder="Enter 6 Digit Email Code"
                onChange={this.handeCode}
                maxLength={6}
                value={code}
                disabled={codeDisable}
                id="code"
                className={styles.SMSInput}
                onBlur={this.blur}
              />
            </Form.Item>
            <div className={styles.btn}>
              <Button
                type="primary"
                className={styles.submitBtn}
                disabled={btnActive || codeActive}
                htmlType="submit"
                loading={modalLoading}
                onClick={this.submit}
              >
                Submit
              </Button>
              <Button onClick={this.handelClose}>Close</Button>
            </div>
          </Form>
          <img className={styles.BrandElement} src={BrandElement} alt="" />
        </Modal>
      </div>
    );
  }
}

export default index;
